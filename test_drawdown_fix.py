"""
测试最大回撤修复
"""

import pandas as pd
import numpy as np
from datetime import datetime

def test_drawdown_calculation():
    """测试回撤计算是否正确"""
    print("=== 测试回撤计算修复 ===")
    
    from vnpy_backtester.scripts.run_multi_position_strategy import run_multi_position_strategy_backtest
    
    # 创建简单的测试数据
    dates = pd.date_range('2023-01-01', periods=100, freq='15min')
    
    # 创建一个有明显回撤的价格序列
    np.random.seed(42)
    prices = [100]
    for i in range(99):
        if i < 30:
            # 前30根K线上涨
            change = np.random.uniform(0.5, 1.0)
        elif i < 60:
            # 中间30根K线下跌（创造回撤）
            change = np.random.uniform(-1.5, -0.5)
        else:
            # 后面恢复
            change = np.random.uniform(0.2, 0.8)
        
        new_price = prices[-1] * (1 + change / 100)
        prices.append(new_price)
    
    df = pd.DataFrame({
        'open': prices[:-1],
        'high': [p * 1.01 for p in prices[:-1]],
        'low': [p * 0.99 for p in prices[:-1]],
        'close': prices[1:],
        'volume': np.random.uniform(1000, 5000, 100),
        'signals': np.random.choice([0, 1, -1], 100, p=[0.9, 0.05, 0.05])
    }, index=dates)
    
    try:
        engine = run_multi_position_strategy_backtest(
            df=df,
            holding_bars=5,
            position_size=100,
            rate=0.001,
            slippage=0.01,
            capital=10000,
            plot_show=False,
            plot_save=False
        )
        
        # 获取统计结果
        stats = engine.calculate_statistics()
        
        print(f"最大回撤: {stats['max_drawdown']:.2f}%")
        print(f"最大回撤百分比: {stats['max_ddpercent']:.2f}%")
        print(f"总收益率: {stats['total_return']:.2f}%")
        print(f"结束资金: {stats['end_balance']:.2f}")
        
        # 验证回撤是否合理（应该在0-100%之间）
        if 0 <= stats['max_drawdown'] <= 100:
            print("✓ 最大回撤数值合理")
            return True
        else:
            print(f"✗ 最大回撤数值异常: {stats['max_drawdown']:.2f}%")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chart_granularity():
    """测试图表是否使用K线级别数据"""
    print("\n=== 测试图表粒度 ===")
    
    from vnpy_backtester.utils.chart_engine import PlotlyChartEngine
    from vnpy_backtester.scripts.run_multi_position_strategy import run_multi_position_strategy_backtest
    
    # 创建测试数据
    dates = pd.date_range('2023-01-01', periods=50, freq='15min')
    
    df = pd.DataFrame({
        'open': [100 + i * 0.1 for i in range(50)],
        'high': [100 + i * 0.1 + 0.5 for i in range(50)],
        'low': [100 + i * 0.1 - 0.5 for i in range(50)],
        'close': [100 + i * 0.1 + 0.2 for i in range(50)],
        'volume': [1000] * 50,
        'signals': [1 if i % 10 == 0 else 0 for i in range(50)]
    }, index=dates)
    
    try:
        # 运行回测
        engine = run_multi_position_strategy_backtest(
            df=df,
            holding_bars=3,
            position_size=100,
            rate=0.001,
            slippage=0.01,
            capital=10000,
            plot_show=False,
            plot_save=False
        )
        
        # 创建图表引擎
        chart_engine = PlotlyChartEngine()
        fig = chart_engine.create_chart(
            engine=engine,
            title="测试图表",
            save_path=None,
            show=False,
            initial_capital=10000
        )
        
        if fig is not None:
            # 检查图表数据点数量
            # 应该有接近50个数据点（K线级别）
            data_points = len(fig.data[0].x) if fig.data else 0
            print(f"图表数据点数量: {data_points}")
            
            # 检查标题是否包含K线标识
            title_text = fig.layout.title.text if fig.layout.title else ""
            if "K线" in title_text or "逐K线" in title_text:
                print("✓ 图表标题正确标识K线级别")
            else:
                print(f"✗ 图表标题未标识K线级别: {title_text}")
            
            # 检查子图标题
            subplot_titles = fig.layout.annotations
            if subplot_titles and any("K线" in str(ann.text) for ann in subplot_titles):
                print("✓ 子图标题正确标识K线级别")
            else:
                print("✗ 子图标题未标识K线级别")
            
            print("✓ 图表创建成功，使用K线级别数据")
            return True
        else:
            print("✗ 图表创建失败")
            return False
            
    except Exception as e:
        print(f"✗ 图表测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试修复效果...")
    
    results = []
    
    # 测试回撤计算
    results.append(test_drawdown_calculation())
    
    # 测试图表粒度
    results.append(test_chart_granularity())
    
    # 总结结果
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n=== 测试总结 ===")
    print(f"成功: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("✓ 所有修复都有效！")
    else:
        print("✗ 部分修复需要进一步检查。")
