"""
Strategy template for backtesting
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Callable

from vnpy_backtester.utils.constant import Direction, Offset, Interval
from vnpy_backtester.objects.object import BarData, TickData, OrderData, TradeData


class StrategyTemplate(ABC):
    """
    Template for strategies.
    """

    author = ""
    parameters = []
    variables = []

    # 默认参数
    order_type = "quantity"  # 下单方式，可选"quantity"(按币数量)或"amount"(按金额)
    position_size = 1  # 每次开仓的数量或金额
    size = 1  # 合约乘数
    commission_rate = 0.0003  # 默认手续费率，将从引擎获取
    slippage_rate = 0.01  # 默认滑点率，将从引擎获取

    def __init__(
        self,
        engine,
        strategy_name: str,
        vt_symbol: str,
        setting: dict = None
    ) -> None:
        """
        Initialize the strategy.
        """
        self.engine = engine
        self.strategy_name = strategy_name
        self.vt_symbol = vt_symbol

        self.inited = False
        self.trading = False
        self.pos = 0

        # 设置策略参数
        self.setting = setting if setting else {}

        # 从设置中获取下单方式和仓位大小
        if "order_type" in self.setting:
            self.order_type = self.setting["order_type"]
        if "position_size" in self.setting:
            self.position_size = self.setting["position_size"]

        # 从引擎获取手续费率和滑点率
        if hasattr(self, "engine"):
            if hasattr(self.engine, "rate"):
                self.commission_rate = self.engine.rate
            if hasattr(self.engine, "slippage"):
                self.slippage_rate = self.engine.slippage
            if hasattr(self.engine, "size"):
                self.size = self.engine.size

    def on_init(self) -> None:
        """
        Callback when strategy is initialized.
        """
        self.inited = True
        self.write_log("Strategy initialized")

        # 记录下单方式和仓位大小
        if self.order_type == "quantity":
            self.write_log(f"下单方式: 按币数量, 每次开仓数量: {self.position_size}")
        else:  # order_type == "amount"
            self.write_log(f"下单方式: 按金额, 每次开仓金额: {self.position_size}")

    def on_start(self) -> None:
        """
        Callback when strategy is started.
        """
        self.trading = True
        self.write_log("Strategy started")

    def on_stop(self) -> None:
        """
        Callback when strategy is stopped.
        """
        self.trading = False
        self.write_log("Strategy stopped")

    def on_tick(self, tick: TickData) -> None:
        """
        Callback of new tick data update.
        """
        pass

    @abstractmethod
    def on_bar(self, bar: BarData) -> None:
        """
        Callback of new bar data update.
        """
        pass

    def on_trade(self, trade: TradeData) -> None:
        """
        Callback of new trade data update.
        """
        pass

    def on_order(self, order: OrderData) -> None:
        """
        Callback of new order data update.
        """
        pass

    def calculate_volume_by_amount(self, price: float, amount: float) -> float:
        """
        计算按金额下单时的数量

        参数:
        price: 价格
        amount: 金额

        返回:
        float: 计算得到的数量
        """
        # 计算可以买入的币数量（考虑手续费和滑点）
        # 解方程：price * volume + price * volume * commission_rate + volume * slippage_rate = amount
        # 简化为：volume * price * (1 + commission_rate) + volume * slippage_rate = amount
        # 得到：volume = amount / (price * (1 + commission_rate) + slippage_rate)
        volume = amount / \
            (price * (1 + self.commission_rate) + self.slippage_rate)
        # 四舍五入到4位小数
        volume = round(volume, 4)
        return volume

    def buy(
        self,
        price: float,
        volume: float = None,
        stop: bool = False,
        lock: bool = False,
        net: bool = False,
        amount: float = None
    ) -> List[str]:
        """
        Send buy order to open a long position.

        参数:
        price: 价格
        volume: 数量，如果为None则使用position_size或根据amount计算
        stop: 是否为止损单
        lock: 是否为锁仓单
        net: 是否为净仓模式
        amount: 金额，如果指定则按金额下单，优先级高于volume
        """
        # 确定下单数量
        if amount is not None:
            # 如果指定了金额，则按金额下单
            actual_volume = self.calculate_volume_by_amount(price, amount)
        elif volume is not None:
            # 如果指定了数量，则按数量下单
            actual_volume = volume
        else:
            # 如果既没有指定金额也没有指定数量，则根据order_type和position_size确定
            if self.order_type == "amount":
                # 按金额下单
                actual_volume = self.calculate_volume_by_amount(
                    price, self.position_size)
            else:
                # 按数量下单
                actual_volume = self.position_size

        # 发送订单
        return self.engine.send_order(
            self,
            Direction.LONG,
            Offset.OPEN,
            price,
            actual_volume,
            stop,
            lock,
            net
        )

    def sell(
        self,
        price: float,
        volume: float = None,
        stop: bool = False,
        lock: bool = False,
        net: bool = False,
        amount: float = None
    ) -> List[str]:
        """
        Send sell order to close a long position.

        参数:
        price: 价格
        volume: 数量，如果为None则使用position_size或根据amount计算
        stop: 是否为止损单
        lock: 是否为锁仓单
        net: 是否为净仓模式
        amount: 金额，如果指定则按金额下单，优先级高于volume
        """
        # 确定下单数量
        if amount is not None:
            # 如果指定了金额，则按金额下单
            actual_volume = self.calculate_volume_by_amount(price, amount)
        elif volume is not None:
            # 如果指定了数量，则按数量下单
            actual_volume = volume
        else:
            # 如果既没有指定金额也没有指定数量，则根据order_type和position_size确定
            if self.order_type == "amount":
                # 按金额下单
                actual_volume = self.calculate_volume_by_amount(
                    price, self.position_size)
            else:
                # 按数量下单
                actual_volume = self.position_size

        # 发送订单
        return self.engine.send_order(
            self,
            Direction.SHORT,
            Offset.CLOSE,
            price,
            actual_volume,
            stop,
            lock,
            net
        )

    def short(
        self,
        price: float,
        volume: float = None,
        stop: bool = False,
        lock: bool = False,
        net: bool = False,
        amount: float = None
    ) -> List[str]:
        """
        Send short order to open a short position.

        参数:
        price: 价格
        volume: 数量，如果为None则使用position_size或根据amount计算
        stop: 是否为止损单
        lock: 是否为锁仓单
        net: 是否为净仓模式
        amount: 金额，如果指定则按金额下单，优先级高于volume
        """
        # 确定下单数量
        if amount is not None:
            # 如果指定了金额，则按金额下单
            actual_volume = self.calculate_volume_by_amount(price, amount)
        elif volume is not None:
            # 如果指定了数量，则按数量下单
            actual_volume = volume
        else:
            # 如果既没有指定金额也没有指定数量，则根据order_type和position_size确定
            if self.order_type == "amount":
                # 按金额下单
                actual_volume = self.calculate_volume_by_amount(
                    price, self.position_size)
            else:
                # 按数量下单
                actual_volume = self.position_size

        # 发送订单
        return self.engine.send_order(
            self,
            Direction.SHORT,
            Offset.OPEN,
            price,
            actual_volume,
            stop,
            lock,
            net
        )

    def cover(
        self,
        price: float,
        volume: float = None,
        stop: bool = False,
        lock: bool = False,
        net: bool = False,
        amount: float = None
    ) -> List[str]:
        """
        Send cover order to close a short position.

        参数:
        price: 价格
        volume: 数量，如果为None则使用position_size或根据amount计算
        stop: 是否为止损单
        lock: 是否为锁仓单
        net: 是否为净仓模式
        amount: 金额，如果指定则按金额下单，优先级高于volume
        """
        # 确定下单数量
        if amount is not None:
            # 如果指定了金额，则按金额下单
            actual_volume = self.calculate_volume_by_amount(price, amount)
        elif volume is not None:
            # 如果指定了数量，则按数量下单
            actual_volume = volume
        else:
            # 如果既没有指定金额也没有指定数量，则根据order_type和position_size确定
            if self.order_type == "amount":
                # 按金额下单
                actual_volume = self.calculate_volume_by_amount(
                    price, self.position_size)
            else:
                # 按数量下单
                actual_volume = self.position_size

        # 发送订单
        return self.engine.send_order(
            self,
            Direction.LONG,
            Offset.CLOSE,
            price,
            actual_volume,
            stop,
            lock,
            net
        )

    def write_log(self, msg: str) -> None:
        """
        Write log message.
        """
        self.engine.write_log(msg, self)

    def get_position(self, vt_symbol: str = None) -> Dict:
        """
        Get current position information from engine.

        Args:
            vt_symbol: Symbol to get position for. If None, return all positions.

        Returns:
            Dict: Position information
        """
        if hasattr(self.engine, 'get_position'):
            return self.engine.get_position(vt_symbol)
        else:
            return {"long": 0.0, "short": 0.0, "net": 0.0}

    def get_position_summary(self) -> str:
        """
        Get a summary string of all positions.

        Returns:
            str: Position summary string
        """
        if hasattr(self.engine, 'get_position_summary'):
            return self.engine.get_position_summary()
        else:
            return "无仓位信息"

    def get_engine_type(self):
        """
        Return engine type.
        """
        return self.engine.get_engine_type()

    def get_pricetick(self):
        """
        Return contract pricetick data.
        """
        return self.engine.get_pricetick(self)

    def load_bar(
        self,
        days: int,
        callback: Callable = None
    ) -> None:
        """
        Load historical bar data for initializing strategy.
        """
        if not callback:
            callback = self.on_bar

        self.engine.load_bar(
            self.vt_symbol,
            days,
            None,
            callback
        )

    def load_tick(self, days: int) -> None:
        """
        Load historical tick data for initializing strategy.
        """
        self.engine.load_tick(self.vt_symbol, days, self.on_tick)
