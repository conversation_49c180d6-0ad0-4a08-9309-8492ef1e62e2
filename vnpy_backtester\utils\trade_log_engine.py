"""
交易日志引擎 - 基于事件驱动架构，用于记录交易记录到日志文件
"""

import os
import datetime
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict

from vnpy_backtester.utils.event import Event, EventEngine, EventType
from vnpy_backtester.objects.object import OrderData, TradeData, BarData, TickData
from vnpy_backtester.utils.constant import Direction, Offset


class TradeLogEngine:
    """
    交易日志引擎 - 基于事件驱动架构，用于记录交易记录到日志文件

    特点:
    1. 自动监听交易事件，记录交易详情
    2. 记录订单状态变化
    3. 记录成交信息，包括价格、数量、方向、开平、手续费、滑点等
    4. 提供详细的交易日志，便于分析和回溯
    """

    def __init__(self, event_engine: EventEngine, log_file: str = "vnpy_backtester/logs/trade_log.log"):
        """
        初始化交易日志引擎

        参数:
        event_engine: 事件引擎实例
        log_file: 日志文件路径，默认为vnpy_backtester/logs/trade_log.log
        """
        self.event_engine = event_engine
        self.log_file = log_file

        # 确保日志文件所在目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)

        # 清空日志文件
        with open(self.log_file, "w", encoding="utf-8") as f:
            f.write(f"===== 交易日志 {datetime.datetime.now()} =====\n")
            f.write("时间,交易ID,订单ID,策略名称,交易类型,合约,方向,开平,价格,数量,手续费,滑点,净盈亏,当前仓位\n")

        # 注册事件处理函数
        self.register_event()

        # 交易记录缓存
        self.trades: Dict[str, TradeData] = {}
        self.orders: Dict[str, OrderData] = {}

        # 策略名称映射
        self.strategy_name_map: Dict[str, str] = {}

        # 持仓管理 - 按合约和方向跟踪持仓
        # 格式: {vt_symbol: {Direction.LONG: [(price, volume), ...], Direction.SHORT: [(price, volume), ...]}}
        self.positions = defaultdict(
            lambda: {Direction.LONG: [], Direction.SHORT: []})

        # 手续费和滑点设置
        self.commission_rate: float = 0.0003
        self.slippage_rate: float = 0.01
        self.size: float = 1

        # 引用交易引擎以获取仓位信息
        self.engine = None

    def set_engine(self, engine):
        """
        设置交易引擎引用，用于获取仓位信息

        参数:
        engine: 交易引擎实例
        """
        self.engine = engine

    def register_event(self) -> None:
        """
        注册事件处理函数
        """
        self.event_engine.register(
            EventType.EVENT_TRADE, self.process_trade_event)
        self.event_engine.register(
            EventType.EVENT_ORDER, self.process_order_event)

    def process_trade_event(self, event: Event) -> None:
        """
        处理成交事件
        """
        trade: TradeData = event.data
        if not trade:
            return

        # 保存成交记录
        self.trades[trade.vt_tradeid] = trade

        # 获取策略名称
        strategy_name = self.get_strategy_name(trade.vt_orderid)

        # 计算手续费和滑点
        commission = self.calculate_commission(trade)
        slippage = self.calculate_slippage(trade)

        # 更新持仓并计算净盈亏
        net_pnl = self.update_position_and_calculate_pnl(trade)

        # 交易类型（开仓/平仓）
        trade_type = "开仓" if trade.offset == Offset.OPEN else "平仓"

        # 获取当前仓位信息
        position_info = "无仓位信息"
        if self.engine and hasattr(self.engine, 'get_position_summary'):
            position_info = self.engine.get_position_summary()

        # 记录交易日志
        log_msg = (
            f"{trade.datetime},"
            f"{trade.tradeid},"
            f"{trade.orderid},"
            f"{strategy_name},"
            f"{trade_type},"
            f"{trade.vt_symbol},"
            f"{trade.direction.value},"
            f"{trade.offset.value},"
            f"{trade.price:.4f},"
            f"{trade.volume:.4f},"
            f"{commission:.4f},"
            f"{slippage:.4f},"
            f"{net_pnl:.4f},"
            f"{position_info}"
        )

        self.write_log(log_msg)

    def process_order_event(self, event: Event) -> None:
        """
        处理订单事件
        """
        order: OrderData = event.data
        if not order:
            return

        # 保存订单记录
        self.orders[order.vt_orderid] = order

        # 如果订单包含策略名称，保存映射关系
        if hasattr(order, "strategy_name") and order.strategy_name:
            self.strategy_name_map[order.vt_orderid] = order.strategy_name

    def get_strategy_name(self, vt_orderid: str) -> str:
        """
        获取策略名称
        """
        # 从映射中获取
        if vt_orderid in self.strategy_name_map:
            return self.strategy_name_map[vt_orderid]

        # 从订单中获取
        if vt_orderid in self.orders and hasattr(self.orders[vt_orderid], "strategy_name"):
            strategy_name = self.orders[vt_orderid].strategy_name
            if strategy_name:
                self.strategy_name_map[vt_orderid] = strategy_name
                return strategy_name

        # 默认返回未知
        return "未知策略"

    def calculate_commission(self, trade: TradeData) -> float:
        """
        计算手续费
        """
        return trade.price * trade.volume * self.size * self.commission_rate

    def calculate_slippage(self, trade: TradeData) -> float:
        """
        计算滑点成本
        """
        return trade.volume * self.size * self.slippage_rate

    def update_position_and_calculate_pnl(self, trade: TradeData) -> float:
        """
        更新持仓并计算净盈亏

        参数:
        trade: 成交数据

        返回:
        float: 净盈亏
        """
        vt_symbol = trade.vt_symbol
        direction = trade.direction
        offset = trade.offset
        price = trade.price
        volume = trade.volume

        # 计算手续费和滑点
        commission = self.calculate_commission(trade)
        slippage = self.calculate_slippage(trade)
        total_cost = commission + slippage

        # 开仓
        if offset == Offset.OPEN:
            # 添加到持仓
            self.positions[vt_symbol][direction].append((price, volume))
            # 开仓没有盈亏，但有成本
            return -total_cost

        # 平仓
        else:
            # 获取对手方向的持仓
            opposite_direction = Direction.SHORT if direction == Direction.LONG else Direction.LONG
            position_list = self.positions[vt_symbol][opposite_direction]

            # 如果没有持仓，返回0
            if not position_list:
                return -total_cost

            # 计算平仓盈亏
            remaining_volume = volume
            pnl = 0.0

            # 按照先开先平的原则计算盈亏
            i = 0
            while i < len(position_list) and remaining_volume > 0:
                pos_price, pos_volume = position_list[i]

                # 计算本次平仓的数量
                close_volume = min(remaining_volume, pos_volume)

                # 计算盈亏
                if direction == Direction.LONG:  # 平空
                    # 开空的价格 - 平空的价格
                    trade_pnl = (pos_price - price) * close_volume * self.size
                else:  # 平多
                    # 平多的价格 - 开多的价格
                    trade_pnl = (price - pos_price) * close_volume * self.size

                pnl += trade_pnl

                # 更新剩余数量
                remaining_volume -= close_volume

                # 更新持仓
                if close_volume == pos_volume:
                    # 如果完全平仓，移除该持仓
                    i += 1
                else:
                    # 如果部分平仓，更新持仓数量
                    position_list[i] = (pos_price, pos_volume - close_volume)
                    break

            # 移除已平仓的持仓
            if i > 0:
                position_list[:] = position_list[i:]

            # 返回净盈亏（盈亏 - 成本）
            return pnl - total_cost

    def write_log(self, msg: str) -> None:
        """
        写入日志

        参数:
        msg: 日志消息
        """
        with open(self.log_file, "a", encoding="utf-8") as f:
            f.write(f"{msg}\n")

    def set_parameters(self, commission_rate: float = None, slippage_rate: float = None, size: float = None) -> None:
        """
        设置参数

        参数:
        commission_rate: 手续费率
        slippage_rate: 滑点率
        size: 合约乘数
        """
        if commission_rate is not None:
            self.commission_rate = commission_rate

        if slippage_rate is not None:
            self.slippage_rate = slippage_rate

        if size is not None:
            self.size = size


# 创建全局交易日志引擎实例（不立即初始化，等待事件引擎传入）
trade_log_engine = None


def initialize_trade_log_engine(event_engine: EventEngine) -> TradeLogEngine:
    """
    初始化交易日志引擎

    参数:
    event_engine: 事件引擎实例

    返回:
    trade_log_engine: 交易日志引擎实例
    """
    global trade_log_engine
    if trade_log_engine is None:
        trade_log_engine = TradeLogEngine(event_engine)
    return trade_log_engine
