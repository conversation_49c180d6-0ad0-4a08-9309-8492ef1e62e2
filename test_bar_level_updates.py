"""
测试逐K线计算更新是否正确工作
"""

import pandas as pd
import numpy as np
from datetime import datetime

# 创建测试数据
def create_test_data():
    """创建简单的测试数据"""
    dates = pd.date_range('2023-01-01', periods=1000, freq='15min')
    
    # 创建简单的价格数据
    np.random.seed(42)
    price = 100
    prices = []
    
    for i in range(len(dates)):
        price += np.random.normal(0, 0.5)
        prices.append(price)
    
    df = pd.DataFrame({
        'open': prices,
        'high': [p * (1 + np.random.uniform(0, 0.01)) for p in prices],
        'low': [p * (1 - np.random.uniform(0, 0.01)) for p in prices],
        'close': prices,
        'volume': np.random.uniform(1000, 5000, len(dates)),
        'signals': np.random.choice([0, 1, -1], len(dates), p=[0.8, 0.1, 0.1])
    }, index=dates)
    
    return df

def test_multi_position_strategy():
    """测试多仓位策略的逐K线计算"""
    print("=== 测试多仓位策略 ===")
    
    from vnpy_backtester.scripts.run_multi_position_strategy import run_multi_position_strategy_backtest
    
    df = create_test_data()
    
    try:
        engine = run_multi_position_strategy_backtest(
            df=df,
            holding_bars=5,
            position_size=100,
            rate=0.001,
            slippage=0.01,
            capital=10000,
            plot_show=False,
            plot_save=False
        )
        print("✓ 多仓位策略测试成功")
        return True
    except Exception as e:
        print(f"✗ 多仓位策略测试失败: {e}")
        return False

def test_ma_cross_strategy():
    """测试MA交叉策略的逐K线计算"""
    print("=== 测试MA交叉策略 ===")
    
    from vnpy_backtester.scripts.run_ma_cross_strategy import run_ma_cross_strategy_backtest
    
    df = create_test_data()
    
    try:
        engine = run_ma_cross_strategy_backtest(
            df=df,
            fast_window=5,
            slow_window=20,
            position_size=1,
            rate=0.001,
            slippage=0.01,
            capital=10000,
            plot_show=False,
            plot_save=False
        )
        print("✓ MA交叉策略测试成功")
        return True
    except Exception as e:
        print(f"✗ MA交叉策略测试失败: {e}")
        return False

def test_chart_engine():
    """测试图表引擎的逐K线计算"""
    print("=== 测试图表引擎 ===")
    
    from vnpy_backtester.utils.chart_engine import PlotlyChartEngine
    from vnpy_backtester.scripts.run_multi_position_strategy import run_multi_position_strategy_backtest
    
    df = create_test_data()
    
    try:
        # 运行回测获取引擎
        engine = run_multi_position_strategy_backtest(
            df=df,
            holding_bars=5,
            position_size=100,
            rate=0.001,
            slippage=0.01,
            capital=10000,
            plot_show=False,
            plot_save=False
        )
        
        # 测试图表引擎
        chart_engine = PlotlyChartEngine()
        fig = chart_engine.create_chart(
            engine=engine,
            title="测试图表",
            save_path=None,
            show=False,
            initial_capital=10000
        )
        
        if fig is not None:
            print("✓ 图表引擎测试成功")
            return True
        else:
            print("✗ 图表引擎返回None")
            return False
            
    except Exception as e:
        print(f"✗ 图表引擎测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试逐K线计算更新...")
    
    results = []
    
    # 测试各个组件
    results.append(test_multi_position_strategy())
    results.append(test_ma_cross_strategy())
    results.append(test_chart_engine())
    
    # 总结结果
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n=== 测试总结 ===")
    print(f"成功: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("✓ 所有测试通过！逐K线计算更新成功。")
    else:
        print("✗ 部分测试失败，需要检查问题。")
