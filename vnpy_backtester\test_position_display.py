"""
测试仓位显示功能
"""

import pandas as pd
from datetime import datetime, timedelta

from vnpy_backtester.engines.engine import BacktestingEngine
from vnpy_backtester.objects.object import BarData
from vnpy_backtester.utils.constant import Exchange
from vnpy_backtester.strategies.multi_position_strategy import MultiPositionStrategy


def test_position_display():
    """
    测试仓位显示功能
    """
    print("=== 测试仓位显示功能 ===")
    
    # 创建简单的测试数据
    start_time = datetime(2025, 1, 1, 9, 0, 0)
    data = []
    
    # 创建10根K线的测试数据
    for i in range(10):
        dt = start_time + timedelta(minutes=15 * i)
        price = 1800 + i * 5  # 价格逐渐上涨
        
        # 设置交易信号
        if i == 1:
            signal = 1  # 第2根K线做多
        elif i == 3:
            signal = -1  # 第4根K线做空
        elif i == 5:
            signal = 1  # 第6根K线做多
        elif i == 7:
            signal = -1  # 第8根K线做空
        else:
            signal = 0  # 其他K线无信号
            
        data.append({
            'datetime': dt,
            'open': price,
            'high': price + 2,
            'low': price - 2,
            'close': price + 1,
            'volume': 1000,
            'signals': signal
        })
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    df.set_index('datetime', inplace=True)
    
    print("测试数据:")
    print(df[['close', 'signals']])
    print()
    
    # 创建Bar对象列表
    bars = []
    for row in df.itertuples():
        bar = BarData(
            symbol="ETHUSDT",
            exchange=Exchange.BINANCE,
            datetime=row.Index,
            open_price=row.open,
            high_price=row.high,
            low_price=row.low,
            close_price=row.close,
            volume=row.volume,
            turnover=row.volume * row.close,
            gateway_name="BACKTEST"
        )
        setattr(bar, "signals", row.signals)
        bars.append(bar)
    
    # 设置回测引擎
    engine = BacktestingEngine()
    engine.set_parameters(
        vt_symbol="ETHUSDT.BINANCE",
        start=df.index[0],
        end=df.index[-1],
        rate=0.0003,
        slippage=0.01,
        capital=10000,
        holding_bars=2,  # 持仓2根K线
        position_size=1000,  # 每次开仓1000元
        order_type="amount"
    )
    
    # 添加策略
    engine.add_strategy(MultiPositionStrategy)
    
    # 加载数据
    engine.history_data = bars
    
    # 运行回测
    print("开始回测...")
    engine.run_backtesting()
    
    # 计算结果
    engine.calculate_result()
    
    # 显示统计指标
    stats = engine.calculate_statistics()
    print("\n=== 回测结果 ===")
    print(f"总交易次数: {stats.get('total_trades', 0)}")
    print(f"初始资金: {stats['capital']:,.2f}")
    print(f"结束资金: {stats['end_balance']:,.2f}")
    print(f"总收益率: {stats['total_return']:,.2f}%")
    
    # 显示最终仓位
    print(f"\n最终仓位: {engine.get_position_summary()}")
    
    print("\n=== 测试完成 ===")
    print("请查看 vnpy_backtester/logs/trade_log.log 文件中的仓位信息")


if __name__ == "__main__":
    test_position_display()
