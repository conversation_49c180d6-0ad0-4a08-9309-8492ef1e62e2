#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试K线级别的统计指标计算
"""

import sys
sys.path.append('.')

import pandas as pd
from vnpy_backtester.scripts.run_multi_position_strategy import run_multi_position_strategy_backtest

def test_bar_level_statistics():
    """测试K线级别的统计指标计算"""
    print('=== 测试K线级别的统计指标计算 ===')
    
    # 读取数据
    try:
        df = pd.read_pickle(r'C:\Users\<USER>\Desktop\kline_data_2025-05-30_14儶57儶15.pkl')
        df['signals'] = df['signal']
        df.index = pd.to_datetime(df.index)
        print('数据加载成功')
    except Exception as e:
        print(f'数据加载失败: {e}')
        return
    
    # 使用较小的数据集进行测试
    df_test = df['2023-07-11':'2023-07-13']
    print(f'测试数据长度: {len(df_test)}条K线')
    
    print('\n=== 对比日级别 vs K线级别统计指标 ===')
    
    # 测试参数
    test_params = {
        'holding_bars': 10,
        'position_size': 1000,
        'rate': 0.0003,
        'slippage': 0.000,
        'capital': 15000,
        'order_type': 'amount',
        'max_positions': 10,
        'plot_show': False,
        'plot_save': False
    }
    
    try:
        # 运行回测
        engine = run_multi_position_strategy_backtest(df=df_test, **test_params)
        
        # 获取日级别统计（原始方法）
        from vnpy_backtester.utils.utility import calculate_statistics
        
        print('计算日级别统计指标...')
        daily_stats = calculate_statistics(
            engine.daily_results,
            15000,
            365,
            engine=engine,
            use_bar_level=False  # 强制使用日级别计算
        )
        
        print('计算K线级别统计指标...')
        bar_stats = calculate_statistics(
            engine.daily_results,
            15000,
            365,
            engine=engine,
            use_bar_level=True  # 使用K线级别计算
        )
        
        print('\n=== 统计指标对比 ===')
        
        # 对比关键指标
        comparison_items = [
            ('最终资金', 'end_balance'),
            ('总收益率', 'total_return'),
            ('年化收益率', 'annual_return'),
            ('最大回撤', 'max_ddpercent'),
            ('夏普比率', 'sharpe_ratio'),
            ('总交易次数', 'total_trades'),
            ('胜率', 'win_rate'),
            ('盈亏比', 'profit_ratio'),
            ('利润因子', 'profit_factor'),
            ('期望收益', 'expected_return'),
            ('平均盈利', 'avg_winning'),
            ('平均亏损', 'avg_losing')
        ]
        
        print(f"{'指标':<12} {'日级别':<15} {'K线级别':<15} {'差异':<15} {'差异%':<10}")
        print("-" * 75)
        
        for name, key in comparison_items:
            daily_val = daily_stats.get(key, 0)
            bar_val = bar_stats.get(key, 0)
            
            if daily_val != 0:
                diff_pct = abs(bar_val - daily_val) / abs(daily_val) * 100
            else:
                diff_pct = 0 if bar_val == 0 else float('inf')
            
            diff = bar_val - daily_val
            
            print(f"{name:<12} {daily_val:<15.2f} {bar_val:<15.2f} {diff:<15.2f} {diff_pct:<10.2f}%")
        
        # 显示K线级别特有的指标
        if 'calculation_method' in bar_stats and bar_stats['calculation_method'] == 'bar_level':
            print('\n=== K线级别特有指标 ===')
            print(f"总K线数: {bar_stats.get('total_bars', 0)}")
            print(f"盈利K线数: {bar_stats.get('profit_bars', 0)}")
            print(f"亏损K线数: {bar_stats.get('loss_bars', 0)}")
            print(f"计算方法: {bar_stats.get('calculation_method', 'unknown')}")
        
        # 评估结果
        print('\n=== 评估结果 ===')
        
        # 检查关键指标的一致性
        key_metrics = ['end_balance', 'total_return', 'total_trades', 'win_rate']
        consistent_metrics = 0
        
        for metric in key_metrics:
            daily_val = daily_stats.get(metric, 0)
            bar_val = bar_stats.get(metric, 0)
            
            if daily_val != 0:
                diff_pct = abs(bar_val - daily_val) / abs(daily_val) * 100
                if diff_pct < 10:  # 10%以内认为一致
                    consistent_metrics += 1
            elif bar_val == 0:
                consistent_metrics += 1
        
        consistency_rate = consistent_metrics / len(key_metrics) * 100
        
        print(f'关键指标一致性: {consistency_rate:.1f}% ({consistent_metrics}/{len(key_metrics)})')
        
        if consistency_rate >= 75:
            print('✅ K线级别统计指标计算成功，与日级别基本一致')
        elif consistency_rate >= 50:
            print('⚠️  K线级别统计指标部分一致，可能需要进一步优化')
        else:
            print('❌ K线级别统计指标差异较大，需要检查计算逻辑')
        
        # 检查图表一致性
        print('\n=== 检查与图表的一致性 ===')
        
        from vnpy_backtester.utils.chart_engine import PlotlyChartEngine
        chart_engine = PlotlyChartEngine()
        
        fig = chart_engine.create_chart_by_bar(
            engine=engine,
            title="一致性检查",
            show=False,
            initial_capital=15000
        )
        
        if fig and len(fig.data) > 0:
            balance_trace = fig.data[0]
            if balance_trace and len(balance_trace.y) > 0:
                chart_final_balance = balance_trace.y[-1]
                bar_final_balance = bar_stats.get('end_balance', 0)
                
                print(f'K线统计最终资金: {bar_final_balance:,.2f}')
                print(f'图表计算最终资金: {chart_final_balance:,.2f}')
                
                chart_diff = abs(chart_final_balance - bar_final_balance)
                chart_diff_pct = chart_diff / bar_final_balance * 100 if bar_final_balance != 0 else 0
                
                print(f'统计与图表差异: {chart_diff:,.2f} ({chart_diff_pct:.2f}%)')
                
                if chart_diff_pct < 1:
                    print('✅ K线级别统计与图表完全一致')
                elif chart_diff_pct < 5:
                    print('✅ K线级别统计与图表基本一致')
                else:
                    print('❌ K线级别统计与图表存在差异')
        
        return True
        
    except Exception as e:
        print(f'测试失败: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_bar_level_statistics()
    
    if success:
        print('\n🎉 K线级别统计指标测试完成！')
        print('现在统计指标与图表使用相同的K线级别计算方法，确保完全一致。')
    else:
        print('\n⚠️  测试中发现问题，需要进一步检查。')
