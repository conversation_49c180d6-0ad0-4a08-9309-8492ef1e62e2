"""
测试最大仓位限制功能
"""

import pandas as pd
from datetime import datetime, timedelta

from vnpy_backtester.engines.engine import BacktestingEngine
from vnpy_backtester.objects.object import BarData
from vnpy_backtester.utils.constant import Exchange
from vnpy_backtester.strategies.multi_position_strategy import MultiPositionStrategy


def test_max_positions():
    """
    测试最大仓位限制功能
    """
    print("=== 测试最大仓位限制功能 ===")
    
    # 创建简单的测试数据 - 连续的做空信号
    start_time = datetime(2025, 1, 1, 9, 0, 0)
    data = []
    
    # 创建20根K线，前15根都有做空信号
    for i in range(20):
        dt = start_time + timedelta(minutes=15 * i)
        price = 1000 + i * 2  # 价格逐渐上涨，适合做空
        
        # 前15根K线都有做空信号
        if i < 15:
            signal = -1  # 做空信号
        else:
            signal = 0  # 无信号
            
        data.append({
            'datetime': dt,
            'open': price,
            'high': price + 1,
            'low': price - 1,
            'close': price,
            'volume': 1000,
            'signals': signal
        })
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    df.set_index('datetime', inplace=True)
    
    print("测试数据:")
    print(df[['close', 'signals']].head(15))
    print()
    
    # 创建Bar对象列表
    bars = []
    for row in df.itertuples():
        bar = BarData(
            symbol="ETHUSDT",
            exchange=Exchange.BINANCE,
            datetime=row.Index,
            open_price=row.open,
            high_price=row.high,
            low_price=row.low,
            close_price=row.close,
            volume=row.volume,
            turnover=row.volume * row.close,
            gateway_name="BACKTEST"
        )
        setattr(bar, "signals", row.signals)
        bars.append(bar)
    
    # 设置回测引擎
    engine = BacktestingEngine()
    engine.set_parameters(
        vt_symbol="ETHUSDT.BINANCE",
        start=df.index[0],
        end=df.index[-1],
        rate=0.0003,
        slippage=0.01,
        capital=100000,  # 充足的资金
        holding_bars=5,  # 持仓5根K线
        position_size=1,  # 每次开仓1个币
        order_type="quantity",
        max_positions=3  # 最大持仓数设为3
    )
    
    # 添加策略
    engine.add_strategy(MultiPositionStrategy)
    
    # 加载数据
    engine.history_data = bars
    
    # 运行回测
    print("开始回测...")
    engine.run_backtesting()
    
    # 显示最终仓位
    print(f"\n最终仓位: {engine.get_position_summary()}")
    
    print("\n=== 测试完成 ===")
    print("请查看 vnpy_backtester/logs/strategy_log.log 和 vnpy_backtester/logs/trade_log.log")


if __name__ == "__main__":
    test_max_positions()
