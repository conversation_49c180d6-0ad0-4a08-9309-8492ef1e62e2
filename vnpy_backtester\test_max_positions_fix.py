"""
测试max_positions限制修复效果
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from vnpy_backtester.engines.engine import BacktestingEngine
from vnpy_backtester.objects.object import BarData
from vnpy_backtester.utils.constant import Exchange, Interval
from vnpy_backtester.strategies.multi_position_strategy import MultiPositionStrategy


def test_max_positions_fix():
    """
    测试max_positions限制是否正常工作
    """
    print("=== 测试max_positions限制修复效果 ===")
    
    # 创建测试数据 - 连续的开仓信号
    dates = pd.date_range(start='2022-01-01', periods=50, freq='15T')
    
    # 创建测试DataFrame
    df = pd.DataFrame({
        'open': np.random.uniform(0.4, 0.5, 50),
        'high': np.random.uniform(0.45, 0.55, 50),
        'low': np.random.uniform(0.35, 0.45, 50),
        'close': np.random.uniform(0.4, 0.5, 50),
        'volume': np.random.uniform(1000, 5000, 50),
        'signals': [1] * 20 + [0] * 10 + [-1] * 20  # 前20个开多，中间10个无信号，后20个开空
    }, index=dates)
    
    # 转换为BarData对象
    bars = []
    for i, (timestamp, row) in enumerate(df.iterrows()):
        bar = BarData(
            symbol="ETHUSDT",
            exchange=Exchange.BINANCE,
            datetime=timestamp,
            interval=Interval.MINUTE,
            volume=row['volume'],
            open_price=row['open'],
            high_price=row['high'],
            low_price=row['low'],
            close_price=row['close'],
            signals=row['signals'],
            remaining_bars=len(df) - i - 1  # 剩余K线数量
        )
        bars.append(bar)
    
    # 设置回测引擎
    engine = BacktestingEngine()
    engine.set_parameters(
        vt_symbol="ETHUSDT.BINANCE",
        start=df.index[0],
        end=df.index[-1],
        rate=0.0003,
        slippage=0.01,
        capital=100000,  # 充足的资金
        holding_bars=5,  # 持仓5根K线
        position_size=1,  # 每次开仓1个币
        order_type="quantity",
        max_positions=3  # 最大持仓数设为3
    )
    
    # 添加策略
    engine.add_strategy(MultiPositionStrategy)
    
    # 加载数据
    engine.history_data = bars
    
    # 运行回测
    print("开始回测...")
    engine.run_backtesting()
    
    # 显示最终仓位
    print(f"\n最终仓位: {engine.get_position_summary()}")
    
    print("\n=== 测试完成 ===")
    print("请查看 vnpy_backtester/logs/strategy_log.log 和 vnpy_backtester/logs/trade_log.log")
    print("检查日志中是否有：")
    print("1. '当前max_positions值' - 确认参数正确传递")
    print("2. '仓位检查' - 确认每次开仓前都检查了仓位限制")
    print("3. '达到最大持仓数限制' - 确认超过限制时拒绝开仓")
    print("4. '当前总仓位数' - 确认开仓后仓位数正确")


if __name__ == "__main__":
    test_max_positions_fix()
