"""
简单测试最大回撤修复
"""

import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

# 添加vnpy_backtester到路径
sys.path.append('vnpy_backtester')

def test_simple():
    """简单测试"""
    try:
        # 创建简单的测试数据
        dates = pd.date_range('2023-01-01', periods=20, freq='15min')
        
        df = pd.DataFrame({
            'open': [100 + i * 0.1 for i in range(20)],
            'high': [100 + i * 0.1 + 0.5 for i in range(20)],
            'low': [100 + i * 0.1 - 0.5 for i in range(20)],
            'close': [100 + i * 0.1 + 0.2 for i in range(20)],
            'volume': [1000] * 20,
            'signals': [1 if i % 5 == 0 else 0 for i in range(20)]
        }, index=dates)
        
        print("测试数据创建成功")
        print(f"数据形状: {df.shape}")
        print(f"信号数量: {df['signals'].sum()}")
        
        # 导入并运行回测
        from scripts.run_multi_position_strategy import run_multi_position_strategy_backtest
        
        engine = run_multi_position_strategy_backtest(
            df=df,
            holding_bars=3,
            position_size=100,
            rate=0.001,
            slippage=0.01,
            capital=10000,
            plot_show=False,
            plot_save=False
        )
        
        # 获取统计结果
        stats = engine.calculate_statistics()
        
        print(f"\n=== 测试结果 ===")
        print(f"最大回撤: {stats['max_drawdown']:.2f}%")
        print(f"总收益率: {stats['total_return']:.2f}%")
        print(f"结束资金: {stats['end_balance']:.2f}")
        
        # 验证回撤是否合理
        if 0 <= stats['max_drawdown'] <= 100:
            print("✓ 最大回撤数值合理")
            return True
        else:
            print(f"✗ 最大回撤数值异常: {stats['max_drawdown']:.2f}%")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始简单测试...")
    success = test_simple()
    
    if success:
        print("✓ 测试成功！")
    else:
        print("✗ 测试失败！")
